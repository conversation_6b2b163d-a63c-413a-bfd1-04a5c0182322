import { supabase } from '@/lib/supabase';
import type { StateCode } from '@/types/access-control';
import type { Competent<PERSON>erson, CPType } from '@/types/competent-person';

export interface CompetentPersonSearchParams {
  contractorId: string;
  searchQuery?: string;
  limit?: number;
  offset?: number;
}

export interface CompetentPersonSearchResult {
  data: CompetentPerson[];
  count: number;
  hasMore: boolean;
}

/**
 * Fetch competent persons with optional search functionality
 * Performs database-level search for better performance with large datasets
 */
export async function fetchCompetentPersonsWithSearch({
  contractorId,
  searchQuery,
  limit = 50,
  offset = 0,
}: CompetentPersonSearchParams): Promise<CompetentPersonSearchResult> {
  if (!contractorId) {
    throw new Error('Contractor ID is required');
  }

  // If no search query, fetch all competent persons
  if (!searchQuery || !searchQuery.trim()) {
    const { data, error, count } = await supabase
      .from('competent_person')
      .select('*', { count: 'exact' })
      .eq('contractor_id', contractorId)
      .is('deleted_at', null)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    return {
      data: (data as unknown as CompetentPerson[]) || [],
      count: count || 0,
      hasMore: (count || 0) > offset + limit,
    };
  }

  // For search queries, use textSearch or a simpler approach
  const trimmedQuery = searchQuery.trim().toLowerCase();

  // TODO: Optimize this to use proper database-level search once database supports it
  // Current approach: Get all data first, then filter in JavaScript as a fallback
  // This ensures compatibility while maintaining search functionality
  const { data, error } = await supabase
    .from('competent_person')
    .select('*')
    .eq('contractor_id', contractorId)
    .is('deleted_at', null)
    .order('created_at', { ascending: false });

  if (error) throw error;

  const allData = (data as unknown as CompetentPerson[]) || [];

  // Filter the results in JavaScript
  const filteredData = allData.filter(
    (person) =>
      person.name.toLowerCase().includes(trimmedQuery) ||
      person.ic_no.toLowerCase().includes(trimmedQuery) ||
      person.cp_type.toLowerCase().includes(trimmedQuery) ||
      (person.phone_no &&
        person.phone_no.toLowerCase().includes(trimmedQuery)) ||
      (person.cp_registeration_no &&
        person.cp_registeration_no.toLowerCase().includes(trimmedQuery)),
  );

  // Apply pagination to filtered results
  const paginatedData = filteredData.slice(offset, offset + limit);

  return {
    data: paginatedData,
    count: filteredData.length,
    hasMore: filteredData.length > offset + limit,
  };
}

/**
 * Fetch all competent persons for statistics (without pagination)
 * Used for summary cards and statistics
 */
export async function fetchAllCompetentPersonsForStats(
  contractorId: string,
): Promise<CompetentPerson[]> {
  if (!contractorId) {
    throw new Error('Contractor ID is required');
  }

  const { data, error } = await supabase
    .from('competent_person')
    .select('*')
    .eq('contractor_id', contractorId)
    .is('deleted_at', null)
    .order('created_at', { ascending: false });

  if (error) {
    throw error;
  }

  return (data as unknown as CompetentPerson[]) || [];
}

export interface CreateCompetentPersonParams {
  contractor_id: string;
  name: string;
  phone_no: string;
  ic_no: string;
  cp_type: 'CP1' | 'CP2' | 'CP3';
}

/**
 * Create a new competent person
 */
export async function createCompetentPerson(
  params: CreateCompetentPersonParams,
): Promise<CompetentPerson> {
  const { data, error } = await supabase
    .from('competent_person')
    .insert(params)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create competent person: ${error.message}`);
  }

  return data as unknown as CompetentPerson;
}

// Admin-specific interfaces and functions
export interface ProjectWithCPs {
  id: string;
  name: string;
  code: string;
  location: string;
  state: string;
  status: string | null;
  contractor_id: string | null;
  contractor_name: string;
  competent_persons: CompetentPerson[];
  cp_stats: {
    total: number;
    by_type: Record<string, number>;
    expired: number;
  };
}

export interface AdminCPSearchParams {
  searchQuery?: string;
  stateFilter?: StateCode;
  cpTypeFilter?: CPType;
  limit?: number;
  offset?: number;
}

export interface AdminCPSearchResult {
  data: ProjectWithCPs[];
  count: number;
  hasMore: boolean;
  totalCPs: number;
  totalProjects: number;
}

/**
 * Fetch projects with their competent persons for admin users
 * Includes proper access control based on admin monitoring scope
 */
export async function fetchProjectsWithCPsForAdmin(
  params: AdminCPSearchParams & {
    userId: string;
    userRole: string;
    adminAccessMode?: string;
    monitoringState?: StateCode;
  },
): Promise<AdminCPSearchResult> {
  const {
    searchQuery,
    stateFilter,
    cpTypeFilter,
    limit = 20,
    offset = 0,
    userId,
    userRole,
    adminAccessMode,
    monitoringState,
  } = params;

  if (userRole !== 'admin') {
    throw new Error('Access denied: Admin role required');
  }

  // Get project IDs where user is a member with accepted status
  const { data: memberProjectIds } = await supabase
    .from('project_users')
    .select('project_id')
    .eq('user_id', userId)
    .eq('status', 'accepted')
    .eq('is_active', true);

  const memberProjectIdList = memberProjectIds?.map((p) => p.project_id) || [];

  if (memberProjectIdList.length === 0) {
    return {
      data: [],
      count: 0,
      hasMore: false,
      totalCPs: 0,
      totalProjects: 0,
    };
  }

  // Build the main query for projects
  let projectQuery = supabase
    .from('projects')
    .select(
      `
      id,
      name,
      code,
      location,
      state,
      status,
      contractor_id,
      contractors!inner(name)
    `,
    )
    .in('id', memberProjectIdList)
    .is('deleted_at', null);

  // Apply state-based filtering for state admins
  if (adminAccessMode === 'state' && monitoringState) {
    projectQuery = projectQuery.eq('state', monitoringState);
  }

  // Apply state filter if provided
  if (stateFilter) {
    projectQuery = projectQuery.eq('state', stateFilter);
  }

  // Apply search query to project name or code
  if (searchQuery && searchQuery.trim()) {
    const trimmedQuery = searchQuery.trim();
    projectQuery = projectQuery.or(
      `name.ilike.%${trimmedQuery}%,code.ilike.%${trimmedQuery}%`,
    );
  }

  const { data: projects, error: projectError } = await projectQuery.order(
    'name',
    { ascending: true },
  );

  if (projectError) throw projectError;

  if (!projects || projects.length === 0) {
    return {
      data: [],
      count: 0,
      hasMore: false,
      totalCPs: 0,
      totalProjects: 0,
    };
  }

  // Get all contractor IDs from the projects
  const contractorIds = [
    ...new Set(
      projects
        .map((p) => p.contractor_id)
        .filter((id): id is string => Boolean(id)),
    ),
  ];

  // Fetch competent persons for all contractors
  let cpQuery = supabase
    .from('competent_person')
    .select('*')
    .in('contractor_id', contractorIds)
    .is('deleted_at', null);

  // Apply CP type filter if provided
  if (cpTypeFilter) {
    cpQuery = cpQuery.eq('cp_type', cpTypeFilter);
  }

  const { data: competentPersons, error: cpError } = await cpQuery.order(
    'name',
    { ascending: true },
  );

  if (cpError) throw cpError;

  // Group CPs by contractor
  const cpsByContractor = (competentPersons || []).reduce(
    (acc: Record<string, CompetentPerson[]>, cp) => {
      if (!acc[cp.contractor_id]) {
        acc[cp.contractor_id] = [];
      }
      acc[cp.contractor_id].push(cp as unknown as CompetentPerson);
      return acc;
    },
    {} as Record<string, CompetentPerson[]>,
  );

  // Build the result with projects and their CPs
  const projectsWithCPs: ProjectWithCPs[] = projects.map((project) => {
    const projectCPs = project.contractor_id
      ? cpsByContractor[project.contractor_id] || []
      : [];

    // Calculate CP statistics
    const cpStats = {
      total: projectCPs.length,
      by_type: projectCPs.reduce(
        (acc: Record<string, number>, cp: CompetentPerson) => {
          acc[cp.cp_type] = (acc[cp.cp_type] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      ),
      expired: projectCPs.filter((cp: CompetentPerson) =>
        cp.cert_exp_date ? new Date(cp.cert_exp_date) < new Date() : false,
      ).length,
    };

    return {
      id: project.id,
      name: project.name,
      code: project.code || '',
      location: project.location || '',
      state: project.state || '',
      status: project.status,
      contractor_id: project.contractor_id,
      contractor_name:
        (project.contractors as { name: string })?.name || 'Unknown Contractor',
      competent_persons: projectCPs,
      cp_stats: cpStats,
    };
  });

  // Apply pagination
  const paginatedData = projectsWithCPs.slice(offset, offset + limit);

  // Calculate unique CPs across all projects
  // Use IC number as the unique identifier for CPs
  const uniqueCPs = new Set();
  projectsWithCPs.forEach((project) => {
    project.competent_persons.forEach((cp) => {
      if (cp.ic_no) {
        uniqueCPs.add(cp.ic_no);
      }
    });
  });

  const totalUniqueCPs = uniqueCPs.size;

  return {
    data: paginatedData,
    count: projectsWithCPs.length,
    hasMore: projectsWithCPs.length > offset + limit,
    totalCPs: totalUniqueCPs,
    totalProjects: projectsWithCPs.length,
  };
}
