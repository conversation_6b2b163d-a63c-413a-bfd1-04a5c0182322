#!/bin/bash

# Database Types Validation Script
# This script ensures your database types are up-to-date and TypeScript compiles correctly

set -e

echo "🔍 Validating Database Types and TypeScript..."

# Check if Supabase is running locally
echo "📡 Checking Supabase status..."
if ! supabase status > /dev/null 2>&1; then
    echo "❌ Supabase is not running locally. Please run 'supabase start' first."
    exit 1
fi

echo "✅ Supabase is running locally"

# Generate fresh database types from local instance
echo "🔄 Generating database types from local Supabase..."
pnpm run db:types:local

# Check if database types file exists and is not empty
if [ ! -f "src/types/database.ts" ] || [ ! -s "src/types/database.ts" ]; then
    echo "❌ Database types file is missing or empty!"
    exit 1
fi

echo "✅ Database types generated successfully"

# Run TypeScript type checking
echo "🔍 Running TypeScript type check..."
pnpm type-check

echo "✅ TypeScript type check passed"

# Run linting
echo "🔍 Running ESLint..."
pnpm lint

echo "✅ ESLint passed"

# Try building the project
echo "🏗️ Building project..."
pnpm build

echo "✅ Build completed successfully"

echo ""
echo "🎉 All validations passed! Your database types are properly configured."
echo ""
echo "💡 If you're still getting CI/CD errors, make sure these GitHub secrets are set:"
echo "   - STG_SUPABASE_PROJECT_REF"
echo "   - STG_SUPABASE_URL" 
echo "   - STG_SUPABASE_ANON_KEY"
echo "   - STG_SUPABASE_SERVICE_ROLE_KEY"
echo "   - SUPABASE_ACCESS_TOKEN"
echo ""
echo "📚 For more info, see: https://supabase.com/docs/guides/cli/github-action"