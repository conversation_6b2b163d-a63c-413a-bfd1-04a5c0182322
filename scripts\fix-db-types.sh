#!/bin/bash

# Quick Fix for Database Types Issues
# This script regenerates database types and validates the setup

set -e

echo "🔧 Quick Fix: Regenerating Database Types..."

# Ensure Supabase is running
echo "📡 Starting Supabase (if not already running)..."
supabase start

# Clean any existing links
echo "🧹 Cleaning previous Supabase links..."
supabase unlink || true

# Regenerate types from local database
echo "🔄 Regenerating database types from local instance..."
pnpm run db:types:local

# Verify the types file was created
if [ -f "src/types/database.ts" ]; then
    echo "✅ Database types file created successfully"
    echo "📊 File size: $(wc -c < src/types/database.ts) bytes"
else
    echo "❌ Failed to create database types file"
    exit 1
fi

# Quick validation
echo "🔍 Quick TypeScript validation..."
pnpm type-check

echo ""
echo "🎉 Database types have been regenerated successfully!"
echo ""
echo "📝 Next steps:"
echo "1. Commit the updated src/types/database.ts file"
echo "2. Push your changes to trigger the CI/CD pipeline"
echo "3. The updated PR validation workflow will now generate types from your remote Supabase"
echo ""
echo "💡 The CI/CD pipeline will now:"
echo "   - Link to your staging Supabase project"
echo "   - Generate fresh database types from the remote database"
echo "   - Use those types for TypeScript compilation"