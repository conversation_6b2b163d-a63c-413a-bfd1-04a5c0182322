import { useUserWithProfile } from '@/hooks/use-auth';
import { useDebounce } from '@/hooks/use-debounce';
import { useQuery } from '@tanstack/react-query';
import {
  fetchAllCompetentPersonsForStats,
  fetchCompetentPersonsWithSearch,
  fetchProjectsWithCPsForAdmin,
  type AdminCPSearchParams,
} from '../services/competent-person-service';

/**
 * Hook for searching competent persons with debounced search
 * Uses database-level search for better performance
 */
export function useCompetentPersonsSearch(
  searchQuery: string = '',
  options?: {
    limit?: number;
    offset?: number;
    debounceMs?: number;
  },
) {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  const { limit = 50, offset = 0, debounceMs = 500 } = options || {};

  // Debounce the search query to avoid excessive database calls
  const debouncedSearchQuery = useDebounce(searchQuery, debounceMs);

  return useQuery({
    queryKey: [
      'competent-persons',
      'search',
      contractorId,
      debouncedSearchQuery,
      limit,
      offset,
    ],
    queryFn: () =>
      fetchCompetentPersonsWithSearch({
        contractorId: contractorId!,
        searchQuery: debouncedSearchQuery,
        limit,
        offset,
      }),
    enabled: !!contractorId,
    staleTime: 30 * 1000, // 30 seconds
    placeholderData: (previousData) => previousData, // Keep previous results while fetching new ones
  });
}

/**
 * Hook for getting all competent persons for statistics
 * Separate from search to avoid affecting stats when searching
 */
export function useCompetentPersonsStats() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['competent-persons', 'stats', contractorId],
    queryFn: () => fetchAllCompetentPersonsForStats(contractorId!),
    enabled: !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes - stats don't need to be as fresh
  });
}

/**
 * Backward compatible hook that maintains the original API
 * Can be used for non-search scenarios or when you want all data
 */
export function useCurrentUserCompetentPersons() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['competent-persons', 'all', contractorId],
    queryFn: () => fetchAllCompetentPersonsForStats(contractorId!),
    enabled: !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook for admin users to search projects with their competent persons
 * Includes proper access control and filtering
 */
export function useAdminProjectsWithCPs(
  searchParams: AdminCPSearchParams = {},
  options?: {
    debounceMs?: number;
  },
) {
  const { data: user } = useUserWithProfile();
  const { debounceMs = 500 } = options || {};

  // Debounce the search query to avoid excessive database calls
  const debouncedSearchQuery = useDebounce(
    searchParams.searchQuery || '',
    debounceMs,
  );

  const debouncedParams = {
    ...searchParams,
    searchQuery: debouncedSearchQuery,
  };

  return useQuery({
    queryKey: [
      'admin-projects-with-cps',
      user?.id,
      debouncedParams.searchQuery,
      debouncedParams.stateFilter,
      debouncedParams.cpTypeFilter,
      debouncedParams.limit,
      debouncedParams.offset,
    ],
    queryFn: () =>
      fetchProjectsWithCPsForAdmin({
        ...debouncedParams,
        userId: user!.id,
        userRole: user!.profile?.user_role || '',
        adminAccessMode: user!.profile?.admin_access_mode || undefined,
        monitoringState: user!.profile?.monitoring_state || undefined,
      }),
    enabled: !!user && user.profile?.user_role === 'admin',
    staleTime: 30 * 1000, // 30 seconds
    placeholderData: (previousData) => previousData, // Keep previous results while fetching new ones
  });
}
